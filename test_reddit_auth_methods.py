#!/usr/bin/env python3
"""
测试不同的Reddit认证方式
"""

import os
from dotenv import load_dotenv
import praw

def test_auth_methods():
    """测试不同认证方式"""
    print("🔍 测试Reddit认证方式...")
    
    # 加载环境变量
    load_dotenv()
    
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    print(f"Client ID: {client_id[:8]}..." if client_id else "Client ID: 未设置")
    print(f"Client Secret: {'已设置' if client_secret else '未设置'}")
    print(f"User Agent: {user_agent}")
    print(f"用户名: {username}")
    print(f"密码: {'已设置' if password and password != 'your_reddit_password' else '未设置'}")
    
    # 方法1: 只读模式（无用户认证）
    print("\n🔍 方法1: 只读模式测试...")
    try:
        reddit_readonly = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # 测试只读访问
        subreddit = reddit_readonly.subreddit('stocks')
        print(f"✓ 只读模式成功")
        print(f"  - 子版块: {subreddit.display_name}")
        print(f"  - 订阅者: {subreddit.subscribers:,}")
        
        # 测试获取帖子
        posts = list(subreddit.hot(limit=3))
        print(f"  - 获取帖子: {len(posts)} 个")
        
        for i, post in enumerate(posts, 1):
            print(f"    {i}. {post.title[:40]}...")
        
        print("✅ 只读模式完全可用！")
        return True
        
    except Exception as e:
        print(f"❌ 只读模式失败: {e}")
    
    # 方法2: 用户认证模式
    if username and password and password != 'your_reddit_password':
        print("\n🔍 方法2: 用户认证模式测试...")
        try:
            reddit_auth = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent,
                username=username,
                password=password
            )
            
            # 测试认证访问
            print(f"✓ 认证用户: {reddit_auth.user.me()}")
            
            subreddit = reddit_auth.subreddit('stocks')
            posts = list(subreddit.hot(limit=2))
            print(f"✓ 认证模式获取帖子: {len(posts)} 个")
            
            print("✅ 用户认证模式成功！")
            return True
            
        except Exception as e:
            print(f"❌ 用户认证失败: {e}")
            if "invalid_grant" in str(e):
                print("  💡 可能需要应用专用密码")
    else:
        print("\n⚠️  跳过用户认证测试（密码未设置）")
    
    return False

def show_password_setup_guide():
    """显示密码设置指南"""
    print("\n" + "="*60)
    print("🔑 Google登录用户的密码设置指南")
    print("="*60)
    
    print("\n方案1: 创建Reddit应用密码（推荐）")
    print("-" * 40)
    print("1. 访问: https://www.reddit.com/prefs/apps")
    print("2. 滚动到页面底部")
    print("3. 找到 'App passwords' 部分")
    print("4. 点击 'generate new password'")
    print("5. 输入应用名称: AI-Hedge-Fund-Bot")
    print("6. 复制生成的密码")
    print("7. 在.env文件中设置: REDDIT_PASSWORD=生成的密码")
    
    print("\n方案2: 设置Reddit账户密码")
    print("-" * 40)
    print("1. 访问: https://www.reddit.com/settings/privacy")
    print("2. 找到 'Change Password' 部分")
    print("3. 设置一个新密码")
    print("4. 在.env文件中设置: REDDIT_PASSWORD=新密码")
    
    print("\n方案3: 仅使用只读模式")
    print("-" * 40)
    print("1. 如果只读模式测试成功，可以继续使用")
    print("2. 只读模式可以获取帖子和评论")
    print("3. 无法进行需要登录的操作（如投票、评论）")
    print("4. 对于数据收集来说通常已经足够")

def main():
    """主函数"""
    print("=" * 60)
    print("Reddit认证方式测试")
    print("=" * 60)
    
    success = test_auth_methods()
    
    if not success:
        show_password_setup_guide()
        return 1
    else:
        print("\n🎉 Reddit API认证成功！")
        print("现在可以开始收集数据了")
        return 0

if __name__ == '__main__':
    exit(main())
